# Contributing to <PERSON><PERSON><PERSON>'s Digital CV

Thank you for your interest in contributing to this digital CV project! While this is primarily a personal CV, I welcome suggestions, bug reports, and improvements that could benefit the broader community.

## 🤝 How to Contribute

### Reporting Issues
If you find any bugs, accessibility issues, or have suggestions for improvements:

1. **Check existing issues** first to avoid duplicates
2. **Create a new issue** with a clear title and description
3. **Include screenshots** if reporting visual bugs
4. **Specify browser/device** information for compatibility issues

### Suggesting Enhancements
For feature requests or enhancements:

1. **Open an issue** with the "enhancement" label
2. **Describe the feature** and its benefits
3. **Explain the use case** and why it would be valuable
4. **Consider accessibility** and mobile compatibility

### Code Contributions
If you'd like to contribute code improvements:

1. **Fork the repository**
2. **Create a feature branch** (`git checkout -b feature/improvement-name`)
3. **Make your changes** following the coding standards below
4. **Test thoroughly** across different browsers and devices
5. **Commit your changes** with clear, descriptive messages
6. **Push to your branch** (`git push origin feature/improvement-name`)
7. **Open a Pull Request** with a detailed description

## 📋 Coding Standards

### HTML
- Use semantic HTML5 elements
- Include proper ARIA labels and roles
- Maintain proper heading hierarchy (h1 → h2 → h3)
- Add descriptive alt text for images
- Ensure keyboard navigation compatibility

### CSS
- Follow mobile-first responsive design
- Use consistent naming conventions
- Maintain the existing color scheme (gold/black/white)
- Ensure cross-browser compatibility
- Optimize for print media

### JavaScript
- Write clean, readable code with comments
- Ensure accessibility (keyboard navigation, screen readers)
- Test functionality across different browsers
- Maintain existing jQuery compatibility
- Follow ES6+ standards where appropriate

## 🎨 Design Guidelines

### Visual Consistency
- Maintain the professional gold, black, and white color scheme
- Use consistent spacing and typography
- Ensure proper contrast ratios for accessibility
- Keep the clean, modern aesthetic

### Responsive Design
- Test on mobile, tablet, and desktop viewports
- Ensure touch-friendly interactions on mobile
- Maintain readability at all screen sizes
- Optimize for both portrait and landscape orientations

### Performance
- Optimize images for web delivery
- Minimize CSS and JavaScript where possible
- Ensure fast loading times
- Test performance on slower connections

## 🧪 Testing

Before submitting contributions, please test:

### Browser Compatibility
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

### Device Testing
- Desktop (1920x1080 and higher)
- Tablet (768px - 1024px)
- Mobile (320px - 767px)

### Accessibility Testing
- Screen reader compatibility
- Keyboard navigation
- Color contrast ratios
- Focus indicators

### Print Testing
- PDF generation quality
- Page breaks and layout
- Print-specific styling

## 📝 Pull Request Guidelines

### PR Description
- Clearly describe what changes were made
- Explain why the changes are necessary
- Include screenshots for visual changes
- Reference any related issues

### Code Quality
- Follow the existing code style
- Include comments for complex logic
- Remove any debugging code
- Ensure no console errors

### Testing
- Test all functionality thoroughly
- Verify responsive design works
- Check accessibility compliance
- Confirm print layout is maintained

## 🚀 Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/kiwiorbit/cv.git
   cd cv
   ```

2. **Open in your preferred editor**
   - VS Code, Sublime Text, or any HTML/CSS editor

3. **Test locally**
   - Open `cv.html` in your browser
   - Use browser dev tools for responsive testing
   - Test print functionality (Ctrl/Cmd + P)

4. **Make changes**
   - Edit HTML, CSS, or JavaScript as needed
   - Test changes across different browsers
   - Ensure accessibility compliance

## 📞 Contact

If you have questions about contributing:

- **GitHub Issues**: For bugs and feature requests
- **Email**: <EMAIL>
- **LinkedIn**: [linkedin.com/in/hash94lens](https://www.linkedin.com/in/hash94lens/)

## 📄 License

By contributing to this project, you agree that your contributions will be licensed under the same MIT License that covers the project.

---

**Thank you for helping make this CV project better!** 🙏
