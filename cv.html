<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> - Web Developer & Digital Creator | Auckland, New Zealand</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="Experienced Web Developer & Digital Creator from Auckland, New Zealand. Specializing in responsive web development, UI/UX design, and modern web technologies. Available for freelance projects.">
    <meta name="keywords" content="web developer, digital creator, frontend developer, UI/UX designer, Auckland developer, New Zealand web developer, responsive design, HTML, CSS, JavaScript, React, freelance developer">
    <meta name="author" content="Shabih Ul Hussain Shah">
    <meta name="robots" content="index, follow">
    <meta name="language" content="English">
    <meta name="revisit-after" content="7 days">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<PERSON><PERSON><PERSON><PERSON> - Web Developer & Digital Creator">
    <meta property="og:description" content="Experienced Web Developer & Digital Creator from Auckland, New Zealand. Specializing in responsive web development, UI/UX design, and modern web technologies.">
    <meta property="og:image" content="myphoto.jpeg">
    <meta property="og:url" content="https://kiwiorbit.github.io/cv/">
    <meta property="og:type" content="profile">
    <meta property="og:site_name" content="Shabih Ul Hussain Shah CV">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Shabih Ul Hussain Shah - Web Developer & Digital Creator">
    <meta name="twitter:description" content="Experienced Web Developer & Digital Creator from Auckland, New Zealand. Specializing in responsive web development and UI/UX design.">
    <meta name="twitter:image" content="myphoto.jpeg">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://kiwiorbit.github.io/cv/">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">

    <!-- Schema.org JSON-LD -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "Shabih Ul Hussain Shah",
      "jobTitle": "Web Developer & Digital Creator",
      "description": "Experienced Web Developer & Digital Creator specializing in responsive web development, UI/UX design, and modern web technologies.",
      "url": "https://kiwiorbit.github.io/cv/",
      "image": "myphoto.jpeg",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "101A Pakuranga Road",
        "addressLocality": "Pakuranga",
        "addressRegion": "Auckland",
        "postalCode": "2010",
        "addressCountry": "New Zealand"
      },
      "email": "<EMAIL>",
      "telephone": "+64 2 2325 8094",
      "knowsAbout": ["Web Development", "UI/UX Design", "JavaScript", "HTML5", "CSS3", "React", "Node.js", "Responsive Design", "Digital Marketing"],
      "alumniOf": [
        {
          "@type": "EducationalOrganization",
          "name": "Udemy",
          "description": "Full Stack Web Development Bootcamp"
        },
        {
          "@type": "EducationalOrganization",
          "name": "Shopee University",
          "description": "Certificate of Graduation (Advanced Class)"
        }
      ],
      "worksFor": {
        "@type": "Organization",
        "name": "Kiwi Orbit New Zealand",
        "description": "Self Employed Web Developer"
      },
      "sameAs": [
        "https://kiwiorbit.github.io/kiwiorbit-pf/",
        "https://www.linkedin.com/in/hash94lens/",
        "https://github.com/kiwiorbit"
      ]
    }
    </script>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- jQuery CDN -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gold: #b5913c;
            --secondary-gold: #a98530;
            --primary-black: #000000;
            --primary-white: #ffffff;
            --gold-shadow: rgba(213, 183, 112, 0.3);
        }

        * {
            font-family: 'Poppins', sans-serif;
        }

        .gold-gradient {
            background: linear-gradient(135deg, var(--primary-gold), var(--secondary-gold));
        }

        .gold-text {
            color: var(--primary-gold);
        }

        .hover-gold:hover {
            color: var(--primary-gold);
            transition: all 0.3s ease;
        }

        .section-divider {
            height: 2px;
            background: linear-gradient(90deg, var(--primary-gold), transparent);
        }

        /* Lightbox Styles */
        .lightbox {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.95);
            z-index: 9999;
            backdrop-filter: blur(10px);
            animation: lightboxFadeIn 0.3s ease-out;
        }

        .lightbox.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .lightbox-content {
            position: relative;
            max-width: 90vw;
            max-height: 90vh;
            animation: lightboxZoomIn 0.4s ease-out;
        }

        .lightbox-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 12px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
        }

        .lightbox-close {
            position: absolute;
            top: -50px;
            right: -10px;
            background: var(--primary-gold);
            color: black;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .lightbox-close:hover {
            background: var(--secondary-gold);
            transform: scale(1.1);
        }

        .lightbox-title {
            position: absolute;
            bottom: -60px;
            left: 0;
            right: 0;
            text-align: center;
            color: white;
            font-size: 18px;
            font-weight: 600;
        }

        .project-image-container {
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .project-image-container:hover {
            transform: scale(1.02);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .project-image-container::after {
            content: '\f00e';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .project-image-container:hover::after {
            opacity: 1;
        }

        @keyframes lightboxFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes lightboxZoomIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .skill-bar {
            background: linear-gradient(90deg, var(--primary-gold), var(--secondary-gold));
            height: 4px;
            border-radius: 2px;
        }
        
        .print-friendly {
            print-color-adjust: exact;
            -webkit-print-color-adjust: exact;
        }
        
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            body {
                font-size: 11px !important;
                line-height: 1.2 !important;
                margin: 0 !important;
                padding: 0 !important;
            }
            .no-print {
                display: none !important;
            }
            .max-w-4xl {
                max-width: none !important;
                margin: 0 !important;
                padding: 0 !important;
                box-shadow: none !important;
            }
            .flex {
                display: flex !important;
            }
            .flex-col {
                flex-direction: column !important;
            }
            .flex-row {
                flex-direction: row !important;
            }
            .lg\:flex-row {
                flex-direction: row !important;
            }
            .lg\:w-1\/3 {
                width: 33.333333% !important;
            }
            .lg\:w-2\/3 {
                width: 66.666667% !important;
            }
            .p-8 {
                padding: 0.75rem !important;
            }
            .p-6 {
                padding: 0.5rem !important;
            }
            .p-4 {
                padding: 0.375rem !important;
            }
            .mb-8 {
                margin-bottom: 0.75rem !important;
            }
            .mb-6 {
                margin-bottom: 0.5rem !important;
            }
            .mb-4 {
                margin-bottom: 0.375rem !important;
            }
            .text-4xl, .text-5xl {
                font-size: 1.25rem !important;
                line-height: 1.3 !important;
            }
            .text-3xl {
                font-size: 1.125rem !important;
            }
            .text-2xl {
                font-size: 1rem !important;
            }
            .text-xl {
                font-size: 0.875rem !important;
            }
            .space-y-6 > * + * {
                margin-top: 0.375rem !important;
            }
            .space-y-4 > * + * {
                margin-top: 0.25rem !important;
            }
            .space-y-3 > * + * {
                margin-top: 0.1875rem !important;
            }
            .h-32 {
                height: 2.5rem !important;
            }
            .w-32 {
                width: 2.5rem !important;
            }
            .gap-8 {
                gap: 0.5rem !important;
            }
            .gap-4 {
                gap: 0.25rem !important;
            }
            .rounded-full {
                border-radius: 50% !important;
            }
            .bg-black {
                background-color: #000000 !important;
            }
            .bg-gray-50 {
                background-color: #f9fafb !important;
            }
            .text-white {
                color: #ffffff !important;
            }
            @page {
                size: A4;
                margin: 0.4in;
            }
            .break-inside-avoid {
                break-inside: avoid;
            }
        }
        
        .animate-fade-in {
            animation: fadeIn 0.8s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .profile-img {
            border: 4px solid var(--primary-gold);
            box-shadow: 0 0 20px var(--gold-shadow);
        }
    </style>
</head>
<body class="bg-white text-black print-friendly">
    <div class="max-w-4xl mx-auto bg-white shadow-2xl">
        <!-- Header Section -->
        <header class="bg-black text-white p-8 relative overflow-hidden" role="banner">
            <div class="absolute inset-0 opacity-10" aria-hidden="true">
                <div class="gold-gradient w-full h-full transform rotate-12 scale-150"></div>
            </div>
            <div class="relative z-10 flex flex-col md:flex-row items-center gap-8">
                <div class="flex-shrink-0">
                    <img src="myphoto.jpeg" alt="Professional headshot of Shabih Ul Hussain Shah, Web Developer from Auckland, New Zealand"
                         class="w-32 h-32 rounded-full object-cover profile-img animate-fade-in cursor-pointer"
                         onclick="openLightbox('myphoto.jpeg', 'Shabih Ul Hussain Shah - Profile Photo')"
                         loading="eager">
                </div>
                <div class="text-center md:text-left flex-grow">
                    <h1 class="text-4xl md:text-5xl font-bold mb-2 animate-fade-in">
                        SHABIH UL HUSSAIN <span class="gold-text">SHAH</span>
                    </h1>
                    <p class="text-xl gold-text mb-4 animate-fade-in" role="doc-subtitle">Web Developer & Digital Creator</p>
                    <p class="text-gray-300 max-w-2xl animate-fade-in">
                        Turning Ideas Into Digital Reality - Passionate web developer creating modern,
                        interactive websites with clean code and intuitive experiences.
                    </p>
                </div>
            </div>
        </header>

        <div class="flex flex-col lg:flex-row">
            <!-- Left Sidebar -->
            <aside class="lg:w-1/3 bg-gray-50 p-6">
                <!-- Contact Information -->
                <section class="mb-8">
                    <h2 class="text-2xl font-bold mb-4 flex items-center">
                        <i class="fas fa-address-card gold-text mr-3"></i>
                        CONTACT
                    </h2>
                    <div class="section-divider mb-4"></div>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt gold-text w-5 mr-3"></i>
                            <span class="text-sm">101A Pakuranga Road, Pakuranga, Auckland, 2010, New Zealand</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-phone gold-text w-5 mr-3"></i>
                            <span class="text-sm">+64 2 2325 9094</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-envelope gold-text w-5 mr-3"></i>
                            <span class="text-sm"><EMAIL></span>
                        </div>
                    </div>
                </section>

                <!-- Skills Section -->
                <section class="mb-8">
                    <h2 class="text-2xl font-bold mb-4 flex items-center">
                        <i class="fas fa-cogs gold-text mr-3"></i>
                        SKILLS
                    </h2>
                    <div class="section-divider mb-4"></div>
                    <div class="space-y-4">
                        <div class="skill-category">
                            <h3 class="font-semibold gold-text mb-2">Frontend</h3>
                            <div class="space-y-1 text-sm">
                                <div>• HTML5</div>
                                <div>• CSS3/Tailwind</div>
                                <div>• JavaScript</div>
                                <div>• jQuery</div>
                                <div>• React/Next.js</div>
                                <div>• Node.js (Learning)</div>
                                <div>• Wordpress (Learning)</div>
                                <div>• Dynamic365 (Learning)</div>
                            </div>
                        </div>
                        <div class="skill-category">
                            <h3 class="font-semibold gold-text mb-2">Design & Tools</h3>
                            <div class="space-y-1 text-sm">
                                <div>• Figma, Canva, UI/UX Design</div>
                                <div>• Photography, Photoshop</div>
                                <div>• DaVinci Resolve, Capcut Editor</div>
                                <div>• ChatGPT, DALL-E</div>
                                <div>• Augment, M365 Copilot</div>
                                <div>• Google Colab</div>
                                <div>• Canva AI Lab</div>
                            </div>
                        </div>
                        <div class="skill-category">
                            <h3 class="font-semibold gold-text mb-2">Business</h3>
                            <div class="space-y-1 text-sm">
                                <div>• E-commerce Management</div>
                                <div>• Stock Management</div>
                                <div>• Retail Administration</div>
                                <div>• Report Writing</div>
                                <div>• Leadership</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Languages -->
                <section class="mb-8">
                    <h2 class="text-2xl font-bold mb-4 flex items-center">
                        <i class="fas fa-globe gold-text mr-3"></i>
                        LANGUAGES
                    </h2>
                    <div class="section-divider mb-4"></div>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>English</span>
                            <span class="gold-text font-semibold">Fluent</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Malay</span>
                            <span class="gold-text font-semibold">Fluent</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Urdu/Hindi</span>
                            <span class="gold-text font-semibold">Fluent</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Punjabi</span>
                            <span class="gold-text font-semibold">Fluent</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Arabic</span>
                            <span class="gold-text font-semibold">Basic</span>
                        </div>
                    </div>
                </section>
            </aside>

            <!-- Main Content -->
            <main class="lg:w-2/3 p-6">
                <!-- About Me -->
                <section class="mb-8">
                    <h2 class="text-3xl font-bold mb-4 flex items-center">
                        <i class="fas fa-user gold-text mr-3"></i>
                        ABOUT ME
                    </h2>
                    <div class="section-divider mb-4"></div>
                    <p class="text-gray-700 leading-relaxed mb-4">
                        Hey there! I'm a passionate web developer from Auckland, New Zealand, who loves creating 
                        modern, interactive websites that come alive with animations. When I'm not in class, 
                        you'll find me coding up responsive landing pages and web apps that blend creativity with functionality!
                    </p>
                    <p class="text-gray-700 leading-relaxed">
                        I'm all about clean code and intuitive experiences that work beautifully across all devices.
                        My journey is just beginning, and I'm constantly exploring new ways to make the web more
                        engaging and fun.
                    </p>
                </section>

                <!-- Experience Section -->
                <section class="mb-8">
                    <h2 class="text-3xl font-bold mb-4 flex items-center">
                        <i class="fas fa-briefcase gold-text mr-3"></i>
                        EXPERIENCE
                    </h2>
                    <div class="section-divider mb-6"></div>

                    <div class="space-y-8">
                        <div class="flex items-start gap-4">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-circle gold-text mr-1 mt-1"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between items-start mb-2">
                                    <h3 class="text-xl font-semibold text-black">Self Employed at Kiwi Orbit NZ</h3>
                                    <p class="text-gray-600 text-sm font-bold">2022 - Present</p>
                                </div>
                                <p class="gold-text font-medium mb-3">Web Developer</p>
                                <ul class="text-gray-700 text-sm space-y-1">
                                    <li>• Developed whanautalk.com for University of Auckland research team</li>
                                    <li>• Designed custom branding and developed website for khuddam.co.nz</li>
                                    <li>• Implementing modern web development best practices</li>
                                    <li>• Focused on user experience, accessibility and client satisfaction</li>
                                </ul>
                            </div>
                        </div>

                        <div class="flex items-start gap-4">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-circle gold-text mr-1 mt-1"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between items-start mb-2">
                                    <h3 class="text-xl font-semibold text-black">NIFC Team Member</h3>
                                    <p class="text-gray-600 text-sm font-bold">Dec 2022 - Feb 2023</p>
                                </div>
                                <p class="gold-text font-medium mb-3">North Island Distribution Center, Auckland, NZ</p>
                                <ul class="text-gray-700 text-sm space-y-1">
                                    <li>• Managing putwall, pallets, and warehouse operations</li>
                                    <li>• Anchoring and heavy lifting responsibilities</li>
                                </ul>
                            </div>
                        </div>

                        <div class="flex items-start gap-4">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-circle gold-text mr-1 mt-1"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between items-start mb-2">
                                    <h3 class="text-xl font-semibold text-black">E-Commerce Shop Manager</h3>
                                    <p class="text-gray-600 text-sm font-bold">May 2017 - Sep 2022</p>
                                </div>
                                <p class="gold-text font-medium mb-3">Jannah Collection Malaysia</p>
                                <ul class="text-gray-700 text-sm space-y-1">
                                    <li>• Managed online retail operations and customer service</li>
                                    <li>• Designed product covers and coordinated model photography sessions</li>
                                    <li>• Implemented inventory control systems</li>
                                    <li>• Led team in retail shop management</li>
                                    <li>• Handled point of sale retail management</li>
                                </ul>
                            </div>
                        </div>

                        <div class="flex items-start gap-4">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-circle gold-text mr-1 mt-1"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between items-start mb-2">
                                    <h3 class="text-xl font-semibold text-black">Manager/Director</h3>
                                    <p class="text-gray-600 text-sm font-bold">Dec 2012 - Apr 2017</p>
                                </div>
                                <p class="gold-text font-medium mb-3">Al Javaid International Sdn Bhd, Malaysia</p>
                                <ul class="text-gray-700 text-sm space-y-1">
                                    <li>• Next Product Selector and budget planning for stock purchases</li>
                                    <li>• Managed business development and client relationships</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>

            </main>
        </div>

        <!-- Education & Training Section - Full Width -->
        <div class="w-full bg-white py-8">
            <div class="px-6">
                <h2 class="text-3xl font-bold mb-4 flex items-center">
                    <i class="fas fa-graduation-cap gold-text mr-3"></i>
                    EDUCATION & TRAINING
                </h2>
                <div class="section-divider mb-6"></div>

                <div class="space-y-4">
                    <div class="bg-gray-50 p-4 rounded-lg border-l-4 shadow-sm" style="border-color: var(--primary-gold);">
                        <h3 class="font-semibold text-lg">Full Stack Web Development Bootcamp</h3>
                        <p class="gold-text">Udemy</p>
                        <p class="text-gray-600 text-sm">2022 - 2024</p>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg border-l-4 shadow-sm" style="border-color: var(--primary-gold);">
                        <h3 class="font-semibold text-lg">Certificate of Graduation (Advanced Class)</h3>
                        <p class="gold-text">Shopee University, Malaysia</p>
                        <p class="text-gray-600 text-sm">2019 - 1-year training in online business management</p>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg border-l-4 shadow-sm" style="border-color: var(--primary-gold);">
                        <h3 class="font-semibold text-lg">Foundation in Commerce</h3>
                        <p class="gold-text">Segi College, Malaysia</p>
                        <p class="text-gray-600 text-sm">2012 (NZQA Level 3)</p>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg border-l-4 shadow-sm" style="border-color: var(--primary-gold);">
                        <h3 class="font-semibold text-lg">Certificate in Malaysian Education SPM</h3>
                        <p class="gold-text">National Secondary School (SMK USJ 8), Malaysia</p>
                        <p class="text-gray-600 text-sm">2011</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Projects Section - Full Width -->
        <div class="w-full bg-gray-50 py-8">
            <div class="px-6">
                <h2 class="text-3xl font-bold mb-4 flex items-center">
                    <i class="fas fa-laptop-code gold-text mr-3"></i>
                    RECENT PROJECTS
                </h2>
                <div class="section-divider mb-6"></div>

                <div class="space-y-6">
                    <div class="bg-white p-6 rounded-lg border-l-4 shadow-sm" style="border-color: var(--primary-gold);">
                        <div class="flex flex-col md:flex-row gap-4">
                            <div class="w-full md:w-1/3">
                                <div class="w-full project-image-container" style="aspect-ratio: 16/9;"
                                     onclick="openLightbox('project-images/whanuatalk.com.png', 'Whanautalk.com')">
                                    <img src="project-images/whanuatalk.com.png" alt="Whanautalk.com"
                                         class="w-full h-full object-cover rounded-lg border">
                                </div>
                            </div>
                            <div class="w-full md:w-2/3">
                                <div class="flex justify-between items-start mb-2">
                                    <h3 class="text-xl font-semibold">Whanautalk.com</h3>
                                    <a href="https://whanautalk.com" target="_blank" rel="noopener noreferrer"
                                       class="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1">
                                        <i class="fas fa-external-link-alt"></i>
                                        Visit Site
                                    </a>
                                </div>
                                <p class="text-gray-700 text-sm mb-3">
                                    Developed a responsive landing page for University of Auckland's research project.
                                    The site features modern design principles, accessibility compliance, and optimized
                                    performance for better user engagement.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg border-l-4 shadow-sm" style="border-color: var(--primary-gold);">
                        <div class="flex flex-col md:flex-row gap-4">
                            <div class="w-full md:w-1/3">
                                <div class="w-full project-image-container" style="aspect-ratio: 16/9;"
                                     onclick="openLightbox('project-images/khuddam.co.nz.png', 'Khuddam.co.nz Website')">
                                    <img src="project-images/khuddam.co.nz.png" alt="Khuddam.co.nz Website"
                                         class="w-full h-full object-cover rounded-lg border">
                                </div>
                            </div>
                            <div class="w-full md:w-2/3">
                                <div class="flex justify-between items-start mb-2">
                                    <h3 class="text-xl font-semibold">Khuddam.co.nz Website</h3>
                                    <a href="https://khuddam.co.nz" target="_blank" rel="noopener noreferrer"
                                       class="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1">
                                        <i class="fas fa-external-link-alt"></i>
                                        Visit Site
                                    </a>
                                </div>
                                <p class="text-gray-700 text-sm mb-3">
                                    Designed and developed a comprehensive website for Khuddam organization.
                                    Features include responsive design, modern UI/UX, and optimized performance
                                    for community engagement and information sharing.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg border-l-4 shadow-sm" style="border-color: var(--primary-gold);">
                        <div class="flex flex-col md:flex-row gap-4">
                            <div class="w-full md:w-1/3">
                                <div class="w-full project-image-container" style="aspect-ratio: 16/9;"
                                     onclick="openLightbox('project-images/nqacademia (working).png', 'NQ Academia Platform (In Development)')">
                                    <img src="project-images/nqacademia (working).png" alt="NQ Academia Platform"
                                         class="w-full h-full object-cover rounded-lg border">
                                </div>
                            </div>
                            <div class="w-full md:w-2/3">
                                <h3 class="text-xl font-semibold mb-2">NQ Academia Platform (In Development)</h3>
                                <p class="text-gray-700 text-sm mb-3">
                                    Currently developing an educational platform for NQ Academia.
                                    The project focuses on creating an intuitive learning management system
                                    with modern web technologies and user-centered design principles.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg border-l-4 shadow-sm" style="border-color: var(--primary-gold);">
                        <div class="flex flex-col md:flex-row gap-4">
                            <div class="w-full md:w-1/3">
                                <div class="w-full project-image-container" style="aspect-ratio: 16/9;"
                                     onclick="openLightbox('project-images/nzbi.nz (working).png', 'nzbi.nz Website (In Development)')">
                                    <img src="project-images/nzbi.nz (working).png" alt="NZBI.nz Website"
                                         class="w-full h-full object-cover rounded-lg border">
                                </div>
                            </div>
                            <div class="w-full md:w-2/3">
                                <h3 class="text-xl font-semibold mb-2">Nzbi.nz Website (In Development)</h3>
                                <p class="text-gray-700 text-sm mb-3">
                                    Developing a professional website for New Zealand Board of Imams.
                                    The project emphasizes clean design, accessibility standards, and
                                    seamless user experience for educational services.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg border-l-4 shadow-sm" style="border-color: var(--primary-gold);">
                        <div class="flex flex-col md:flex-row gap-4">
                            <div class="w-full md:w-1/3">
                                <div class="w-full project-image-container" style="aspect-ratio: 16/9;"
                                     onclick="openLightbox('project-images/Lisan Ul Quran book cover Published.jpg', 'Lisan Ul Quran Book Cover Design')">
                                    <img src="project-images/Lisan Ul Quran book cover Published.jpg" alt="Lisan Ul Quran Book Cover"
                                         class="w-full h-full object-cover rounded-lg border">
                                </div>
                            </div>
                            <div class="w-full md:w-2/3">
                                <h3 class="text-xl font-semibold mb-2">Lisan Ul Quran Book Cover Design</h3>
                                <p class="text-gray-700 text-sm mb-3">
                                    Created a professional book cover design for "Lisan Ul Quran" publication.
                                    The design incorporates traditional Islamic aesthetics with modern typography
                                    and layout principles for enhanced visual appeal.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg border-l-4 shadow-sm" style="border-color: var(--primary-gold);">
                        <div class="flex flex-col md:flex-row gap-4">
                            <div class="w-full md:w-1/3">
                                <div class="w-full project-image-container" style="aspect-ratio: 16/9;"
                                     onclick="openLightbox('project-images/world refugee day presentation.jpg', 'World Refugee Day Presentation Design')">
                                    <img src="project-images/world refugee day presentation.jpg" alt="World Refugee Day Presentation"
                                         class="w-full h-full object-cover rounded-lg border">
                                </div>
                            </div>
                            <div class="w-full md:w-2/3">
                                <h3 class="text-xl font-semibold mb-2">World Refugee Day Presentation Design</h3>
                                <p class="text-gray-700 text-sm mb-3">
                                    Created a comprehensive presentation design for World Refugee Day event at University of Auckland.
                                    The presentation features consistent branding, engaging visuals, and
                                    clear information hierarchy to effectively communicate the message.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Social Footer -->
        <footer class="bg-black text-white py-8">
            <div class="flex justify-center items-center space-x-8">
                <a href="https://kiwiorbit.github.io/kiwiorbit-pf/" target="_blank" rel="noopener noreferrer"
                   class="flex flex-col items-center group hover:text-yellow-400 transition-colors duration-300">
                    <i class="fas fa-globe text-2xl mb-2 group-hover:scale-110 transition-transform duration-300"></i>
                    <span class="text-sm">Portfolio</span>
                </a>

                <a href="https://www.linkedin.com/in/hash94lens/" target="_blank" rel="noopener noreferrer"
                   class="flex flex-col items-center group hover:text-blue-400 transition-colors duration-300">
                    <i class="fab fa-linkedin text-2xl mb-2 group-hover:scale-110 transition-transform duration-300"></i>
                    <span class="text-sm">LinkedIn</span>
                </a>

                <a href="https://github.com/kiwiorbit" target="_blank" rel="noopener noreferrer"
                   class="flex flex-col items-center group hover:text-gray-400 transition-colors duration-300">
                    <i class="fab fa-github text-2xl mb-2 group-hover:scale-110 transition-transform duration-300"></i>
                    <span class="text-sm">GitHub</span>
                </a>
            </div>

            <div class="text-center mt-6 text-gray-400 text-sm">
                <p>&copy; 2025 Shabih Ul Hussain Shah. All rights reserved.</p>
            </div>
        </footer>

        <!-- Lightbox Modal -->
        <div id="lightbox" class="lightbox" onclick="closeLightbox(event)">
            <div class="lightbox-content">
                <button class="lightbox-close" onclick="closeLightbox()">&times;</button>
                <img id="lightbox-image" class="lightbox-image" src="" alt="">
                <div id="lightbox-title" class="lightbox-title"></div>
            </div>
        </div>
    </div>

    <script>
        // Lightbox Functions
        function openLightbox(imageSrc, title) {
            const lightbox = document.getElementById('lightbox');
            const lightboxImage = document.getElementById('lightbox-image');
            const lightboxTitle = document.getElementById('lightbox-title');

            lightboxImage.src = imageSrc;
            lightboxImage.alt = title;
            lightboxTitle.textContent = title;

            lightbox.classList.add('active');
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }

        function closeLightbox(event) {
            // Only close if clicking on the overlay or close button, not the image
            if (event && event.target.closest('.lightbox-content') && !event.target.classList.contains('lightbox-close')) {
                return;
            }

            const lightbox = document.getElementById('lightbox');
            lightbox.classList.remove('active');
            document.body.style.overflow = 'auto'; // Restore scrolling
        }

        // Close lightbox with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeLightbox();
            }
        });

        $(document).ready(function() {
            // Animate elements on scroll
            $(window).scroll(function() {
                $('.animate-fade-in').each(function() {
                    var elementTop = $(this).offset().top;
                    var elementBottom = elementTop + $(this).outerHeight();
                    var viewportTop = $(window).scrollTop();
                    var viewportBottom = viewportTop + $(window).height();

                    if (elementBottom > viewportTop && elementTop < viewportBottom) {
                        $(this).addClass('animate-fade-in');
                    }
                });
            });

            // Smooth hover effects
            $('.hover-gold').hover(
                function() {
                    $(this).addClass('gold-text');
                },
                function() {
                    $(this).removeClass('gold-text');
                }
            );

            // Print button functionality
            $('body').append('<button class="fixed bottom-4 right-4 text-black p-3 rounded-full shadow-lg no-print transition-colors" style="background-color: var(--primary-gold);" onmouseover="this.style.backgroundColor=\'var(--secondary-gold)\'" onmouseout="this.style.backgroundColor=\'var(--primary-gold)\'" onclick="window.print()"><i class="fas fa-print"></i></button>');
        });
    </script>
</body>
</html>
